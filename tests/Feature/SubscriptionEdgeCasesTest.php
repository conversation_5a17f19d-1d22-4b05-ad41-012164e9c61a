<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\PaddleTransaction;
use App\Services\SubscriptionService;
use App\Services\PaddleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Mockery;
use Tests\TestCase;

class SubscriptionEdgeCasesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;
    protected $user;
    protected $freePlan;
    protected $premiumPlan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create pricing plans
        $this->createPricingPlans();
        
        // Create test user
        $this->createTestUser();
    }

    private function createPricingPlans(): void
    {
        PricingPlan::truncate();

        $this->freePlan = PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'search_limit' => 20,
            'is_active' => true,
            'is_default' => true,
        ]);

        $this->premiumPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'search_limit' => -1,
            'is_active' => true,
            'paddle_price_id_monthly' => 'pri_test_monthly',
        ]);
    }

    private function createTestUser(): void
    {
        $this->user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 0,
            'daily_reset' => today(),
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
    }

    /** @test */
    public function failed_payment_handling_works_correctly()
    {
        $this->actingAs($this->user);

        // Mock failed Paddle checkout
        $mockPaddleService = Mockery::mock(PaddleService::class);
        $mockPaddleService->shouldReceive('isDevelopmentMode')->andReturn(false);
        $mockPaddleService->shouldReceive('createCheckoutSession')
            ->andReturn(null); // Simulate failure

        $this->app->instance(PaddleService::class, $mockPaddleService);

        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(500);
        $response->assertJson([
            'error' => 'Failed to create checkout session'
        ]);

        // Verify user remains on free plan
        $this->user->refresh();
        $this->assertEquals('free', $this->user->subscription_plan);
        $this->assertFalse($this->user->isPremium());
    }

    /** @test */
    public function session_handling_during_checkout_process()
    {
        $this->actingAs($this->user);

        // Start checkout process
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(200);
        $transactionId = $response->json('transaction_id');

        // Simulate session timeout by clearing session
        Session::flush();

        // Try to access checkout page with transaction ID
        $response = $this->get('/paddle/checkout?_ptxn=' . $transactionId);
        
        // Should still work as transaction is stored in database
        $response->assertStatus(200);
    }

    /** @test */
    public function subscription_tier_switching_edge_cases()
    {
        $this->actingAs($this->user);

        // Create multiple subscriptions (edge case)
        $subscription1 = Subscription::create([
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subDays(10),
            'current_period_end' => now()->addDays(20),
        ]);

        $subscription2 = Subscription::create([
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subDays(5),
            'current_period_end' => now()->addDays(25),
        ]);

        $this->user->update(['subscription_plan' => 'premium']);

        // Should get the most recent active subscription
        $activeSubscription = $this->user->activeSubscription;
        $this->assertEquals($subscription2->id, $activeSubscription->id);

        // Cancel subscription should cancel all active ones
        $this->subscriptionService->cancelSubscription($this->user);

        $subscription1->refresh();
        $subscription2->refresh();
        $this->assertEquals('cancelled', $subscription1->status);
        $this->assertEquals('cancelled', $subscription2->status);
    }

    /** @test */
    public function search_limit_accuracy_across_multiple_sessions()
    {
        $this->actingAs($this->user);

        // Perform searches in first "session"
        for ($i = 0; $i < 10; $i++) {
            $response = $this->get('/search/results?q=test' . $i);
            $response->assertStatus(200);
        }

        $this->user->refresh();
        $this->assertEquals(10, $this->user->search_count);

        // Simulate new session by clearing cache and session
        Cache::flush();
        Session::flush();

        // Continue searching in "new session"
        for ($i = 10; $i < 20; $i++) {
            $response = $this->get('/search/results?q=test' . $i);
            $response->assertStatus(200);
        }

        $this->user->refresh();
        $this->assertEquals(20, $this->user->search_count);

        // Next search should be blocked
        $response = $this->get('/search/results?q=blocked');
        $response->assertStatus(302);
        $response->assertRedirect(route('subscription.plans'));
    }

    /** @test */
    public function concurrent_search_requests_handling()
    {
        $this->user->update(['search_count' => 19]); // One search left
        $this->actingAs($this->user);

        // Simulate concurrent requests by directly calling the service
        $results = [];
        
        // This simulates what would happen if two requests hit at the same time
        DB::transaction(function () use (&$results) {
            $canSearch1 = $this->subscriptionService->canUserSearch($this->user);
            $canSearch2 = $this->subscriptionService->canUserSearch($this->user);
            
            $results = [$canSearch1, $canSearch2];
        });

        // Both should be able to search since we check before incrementing
        $this->assertTrue($results[0]);
        $this->assertTrue($results[1]);

        // But after one search, the next should be blocked
        $this->user->incrementSearchCount();
        $this->user->refresh();
        $this->assertEquals(20, $this->user->search_count);
        $this->assertFalse($this->subscriptionService->canUserSearch($this->user));
    }

    /** @test */
    public function timezone_handling_for_daily_reset()
    {
        // Set user's daily reset to yesterday in different timezone
        $yesterday = Carbon::yesterday('America/New_York');
        $this->user->update([
            'search_count' => 20,
            'daily_reset' => $yesterday->toDateString(),
        ]);

        $this->actingAs($this->user);

        // Should reset based on server timezone, not user timezone
        $this->assertTrue($this->subscriptionService->canUserSearch($this->user));

        // Perform search to trigger reset
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);

        $this->user->refresh();
        $this->assertEquals(1, $this->user->search_count);
        $this->assertEquals(today()->toDateString(), $this->user->daily_reset);
    }

    /** @test */
    public function subscription_expiry_edge_cases()
    {
        $this->actingAs($this->user);

        // Create subscription that expires today
        $subscription = Subscription::create([
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subMonth(),
            'current_period_end' => now()->endOfDay(), // Expires today
        ]);

        $this->user->update(['subscription_plan' => 'premium']);

        // Should still be active until end of day
        $this->assertTrue($subscription->isActive());
        $this->assertTrue($this->user->isPremium());

        // Create subscription that expired yesterday
        $expiredSubscription = Subscription::create([
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subMonth(),
            'current_period_end' => yesterday(),
        ]);

        // Should not be active
        $this->assertFalse($expiredSubscription->isActive());
    }

    /** @test */
    public function database_transaction_rollback_on_subscription_creation_failure()
    {
        $this->actingAs($this->user);

        // Mock a scenario where subscription creation partially fails
        DB::shouldReceive('transaction')
            ->once()
            ->andThrow(new \Exception('Database error'));

        try {
            $this->subscriptionService->createPremiumSubscription($this->user);
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            $this->assertEquals('Database error', $e->getMessage());
        }

        // Verify user state wasn't changed
        $this->user->refresh();
        $this->assertEquals('free', $this->user->subscription_plan);
        $this->assertFalse($this->user->isPremium());

        // Verify no subscription was created
        $this->assertEquals(0, $this->user->subscriptions()->count());
    }

    /** @test */
    public function invalid_pricing_plan_handling()
    {
        $this->actingAs($this->user);

        // Try to checkout with non-existent plan
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => 99999,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(404);

        // Try to checkout with inactive plan
        $inactivePlan = PricingPlan::factory()->create([
            'name' => 'inactive',
            'is_active' => false,
        ]);

        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $inactivePlan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(404);
    }

    /** @test */
    public function search_count_reset_race_condition()
    {
        // Set user with yesterday's reset date
        $this->user->update([
            'search_count' => 15,
            'daily_reset' => yesterday()->toDateString(),
        ]);

        $this->actingAs($this->user);

        // Simulate multiple concurrent requests that would trigger reset
        $promises = [];
        for ($i = 0; $i < 3; $i++) {
            $promises[] = function () {
                return $this->subscriptionService->canUserSearch($this->user);
            };
        }

        // Execute "concurrently" (simulated)
        $results = array_map(function ($promise) {
            return $promise();
        }, $promises);

        // All should return true (can search)
        foreach ($results as $result) {
            $this->assertTrue($result);
        }

        // But reset should only happen once
        $this->user->refresh();
        $this->assertEquals(0, $this->user->search_count);
        $this->assertEquals(today()->toDateString(), $this->user->daily_reset);
    }

    /** @test */
    public function subscription_status_consistency_check()
    {
        $this->actingAs($this->user);

        // Create subscription but don't update user's subscription_plan
        $subscription = Subscription::create([
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subDays(10),
            'current_period_end' => now()->addDays(20),
        ]);

        // User still shows as free in user table
        $this->assertEquals('free', $this->user->subscription_plan);

        // But should be detected as premium through relationship
        $this->assertTrue($this->user->activeSubscription !== null);
        $this->assertTrue($this->user->activeSubscription->isActive());

        // isPremium should check the actual subscription, not just the field
        $this->user->update(['subscription_plan' => 'premium']);
        $this->assertTrue($this->user->isPremium());
    }
}
