<?php

// Set memory limit for both web and CLI requests to prevent exhaustion
$memoryLimit = env('PHP_MEMORY_LIMIT', '512M');
ini_set('memory_limit', $memoryLimit);

use App\Http\Middleware\AdminRateLimit;
use App\Http\Middleware\CheckImpersonationExpiry;
use App\Http\Middleware\CheckoutDebugMiddleware;
use App\Http\Middleware\CheckUserStatus;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\RequireOtpVerification;
use App\Http\Middleware\ShurjoPayDebugMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->validateCsrfTokens(except: [
            'webhooks/*',
            'email/track/*',
        ]);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            CheckImpersonationExpiry::class,
            CheckUserStatus::class,
        ]);

        $middleware->alias([
            'admin.rate_limit' => AdminRateLimit::class,
            'admin.2fa' => RequireOtpVerification::class,
            'checkout.debug' => CheckoutDebugMiddleware::class,
            'shurjopay.debug' => ShurjoPayDebugMiddleware::class,
            'guest.search.rate_limit' => \App\Http\Middleware\GuestSearchRateLimit::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
