import { useEffect, useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuItem,
    NavigationMenuList,
    NavigationMenuTrigger,
    NavigationMenuContent,
    NavigationMenuLink
} from '@/components/ui/navigation-menu';
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Menu, Search, LayoutGrid } from 'lucide-react';
import AppLogo from './app-logo';
import { useAdmin } from '@/hooks/use-admin';
import { type SharedData } from '@/types';

interface MenuItem {
    id: number;
    title: string;
    url: string;
    target: string;
    children?: MenuItem[];
}

interface NavbarConfig {
    navbar_enabled: boolean;
    navbar_menu_id: number | null;
    navbar_background_color: string;
    navbar_text_color: string;
    navbar_logo_position: string;
    navbar_show_search: boolean;
    navbar_sticky: boolean;
    navbar_style: string;
    menu_items: MenuItem[];
}

interface DynamicNavbarProps {
    className?: string;
    showSearch?: boolean;
    showUserMenu?: boolean;
    showAuthButtons?: boolean;
}

export default function DynamicNavbar({
    className = '',
    showSearch = true,
    showUserMenu = true,
    showAuthButtons = true
}: DynamicNavbarProps) {
    const [config, setConfig] = useState<NavbarConfig | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const page = usePage<SharedData>();
    const { auth } = page.props;
    const isAdmin = useAdmin();

    useEffect(() => {
        const fetchNavbarConfig = async () => {
            try {
                const response = await fetch('/api/navbar-config');
                if (!response.ok) {
                    throw new Error(`Failed to fetch navbar configuration: ${response.status} ${response.statusText}`);
                }
                const data = await response.json();

                // Validate the response data structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid navbar configuration data received');
                }

                // Ensure menu_items is an array
                if (data.menu_items && !Array.isArray(data.menu_items)) {
                    console.warn('menu_items is not an array, converting to empty array');
                    data.menu_items = [];
                }

                console.log('Navbar config loaded successfully:', {
                    enabled: data.navbar_enabled,
                    menuId: data.navbar_menu_id,
                    itemCount: data.menu_items?.length || 0
                });

                setConfig(data);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Unknown error';
                console.error('Error fetching navbar config:', {
                    error: errorMessage,
                    url: '/api/navbar-config',
                    timestamp: new Date().toISOString()
                });
                setError(errorMessage);
            } finally {
                setLoading(false);
            }
        };

        fetchNavbarConfig();
    }, []);

    // Return null while loading or if navbar is disabled
    if (loading || !config || !config.navbar_enabled) {
        return null;
    }

    const navbarStyle = {
        backgroundColor: config.navbar_background_color,
        color: config.navbar_text_color,
    };

    const stickyClass = config.navbar_sticky ? 'sticky top-0 z-50' : '';
    const styleClass = config.navbar_style === 'minimal' ? 'border-b-0' : 'border-b border-gray-200 dark:border-gray-700';

    const renderLogo = () => (
        <Link href="/" className="flex items-center space-x-2">
            <AppLogo />
        </Link>
    );

    // Helper function to render dropdown items
    const renderDropdownItem = (item: MenuItem) => {
        const target = item.target || '_self';

        // Handle invalid URLs gracefully
        if (!item.url || item.url === 'null' || item.url === 'undefined') {
            return (
                <div key={item.id} className="flex items-center justify-start px-3 py-2 text-sm text-gray-400 cursor-not-allowed rounded-md text-left">
                    {item.title}
                </div>
            );
        }

        const href = item.url;
        // Determine if it's an external link
        const isExternal = href.startsWith('http://') || href.startsWith('https://') || href.startsWith('//');

        if (isExternal) {
            return (
                <NavigationMenuLink asChild key={item.id}>
                    <a
                        href={href}
                        target={target}
                        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
                        className="flex items-center justify-start px-3 py-2 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground transition-colors text-left"
                    >
                        {item.title}
                    </a>
                </NavigationMenuLink>
            );
        }

        return (
            <NavigationMenuLink asChild key={item.id}>
                <Link
                    href={href}
                    className="flex items-center justify-start px-3 py-2 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground transition-colors text-left"
                >
                    {item.title}
                </Link>
            </NavigationMenuLink>
        );
    };

    const renderMenuItem = (item: MenuItem, isMobile = false) => {
        // Validate item data and provide fallbacks
        if (!item || !item.id || !item.title) {
            console.warn('Invalid menu item data:', item);
            return null;
        }

        const hasChildren = item.children && Array.isArray(item.children) && item.children.length > 0;

        if (hasChildren) {
            if (isMobile) {
                // Mobile: Render as collapsible section
                return (
                    <div key={item.id} className="py-2">
                        <span className="block text-lg font-medium">
                            {item.title}
                        </span>
                        <div className="ml-4 mt-2 space-y-2">
                            {item.children?.map(child => renderMenuItem(child, isMobile))}
                        </div>
                    </div>
                );
            } else {
                // Desktop: Render as dropdown with NavigationMenuTrigger and NavigationMenuContent
                return (
                    <>
                        <NavigationMenuTrigger className="group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[active=true]:bg-accent/50 data-[state=open]:bg-accent/50 data-[active=true]:text-accent-foreground transition-colors">
                            {item.title}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent className="absolute top-full left-0 z-50 w-auto bg-popover text-popover-foreground shadow-md border rounded-md data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52">
                            <div className="flex flex-col w-[280px] gap-1 p-2 text-left">
                                {item.children?.map(child => renderDropdownItem(child))}
                            </div>
                        </NavigationMenuContent>
                    </>
                );
            }
        }

        // Render leaf items as links
        const target = item.target || '_self';

        // Handle invalid URLs gracefully
        if (!item.url || item.url === 'null' || item.url === 'undefined') {
            console.warn('Menu item has invalid URL:', item);
            return (
                <span
                    key={item.id}
                    className={`text-gray-400 cursor-not-allowed ${isMobile ? "block py-2 text-lg" : ""}`}
                    title="Invalid link"
                >
                    {item.title}
                </span>
            );
        }

        // At this point, we know item.url is valid
        const href = item.url;

        // Determine if it's an external link
        const isExternal = href.startsWith('http://') || href.startsWith('https://') || href.startsWith('//');

        if (isMobile) {
            if (isExternal) {
                return (
                    <a
                        key={item.id}
                        href={href}
                        target={target}
                        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
                        className="block py-2 text-lg hover:text-gray-600 transition-colors"
                    >
                        {item.title}
                    </a>
                );
            }

            return (
                <Link
                    key={item.id}
                    href={href}
                    className="block py-2 text-lg hover:text-gray-600 transition-colors"
                >
                    {item.title}
                </Link>
            );
        }

        // Desktop: Use NavigationMenuLink for proper accessibility
        if (isExternal) {
            return (
                <NavigationMenuLink asChild key={item.id}>
                    <a
                        href={href}
                        target={target}
                        rel={target === '_blank' ? 'noopener noreferrer' : undefined}
                        className="group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 transition-colors"
                    >
                        {item.title}
                    </a>
                </NavigationMenuLink>
            );
        }

        return (
            <NavigationMenuLink asChild key={item.id}>
                <Link
                    href={href}
                    className="group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 transition-colors"
                >
                    {item.title}
                </Link>
            </NavigationMenuLink>
        );
    };

    const renderDesktopMenu = () => {
        if (!config.menu_items || config.menu_items.length === 0) return null;

        return (
            <NavigationMenu className="hidden lg:flex" viewport={false}>
                <NavigationMenuList className="space-x-6">
                    {config.menu_items.map(item => (
                        <NavigationMenuItem key={item.id}>
                            {renderMenuItem(item)}
                        </NavigationMenuItem>
                    ))}
                </NavigationMenuList>
            </NavigationMenu>
        );
    };

    const renderMobileMenu = () => {
        if (!config.menu_items || config.menu_items.length === 0) return null;

        return (
            <div className="lg:hidden">
                <Sheet>
                    <SheetTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9 rounded-lg"
                        >
                            <Menu className="h-5 w-5" />
                            <span className="sr-only">Open navigation menu</span>
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80">
                        <SheetHeader>
                            <SheetTitle>Navigation</SheetTitle>
                        </SheetHeader>
                        <div className="mt-6 space-y-4">
                            {config.menu_items.map(item => renderMenuItem(item, true))}
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        );
    };

    const renderSearchButton = () => {
        // Hide search button for authenticated users
        if (!config.navbar_show_search || !showSearch || auth.user) return null;

        return (
            <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-lg"
                onClick={() => {
                    // Trigger global search command
                    const event = new KeyboardEvent('keydown', {
                        key: 'k',
                        ctrlKey: true,
                        metaKey: true,
                    });
                    document.dispatchEvent(event);
                }}
            >
                <Search className="h-5 w-5" />
                <span className="sr-only">Search</span>
            </Button>
        );
    };

    const renderAuthButtons = () => {
        if (!showAuthButtons || auth.user) return null;

        return (
            <div className="flex items-center space-x-2">
                <Link href={route('login')}>
                    <Button variant="ghost" size="sm">
                        Log in
                    </Button>
                </Link>
                <Link href={route('register')}>
                    <Button size="sm">
                        Sign up
                    </Button>
                </Link>
            </div>
        );
    };

    const renderDashboardButton = () => {
        if (!auth.user) return null;

        const dashboardRoute = isAdmin ? 'admin.dashboard' : 'dashboard';
        const dashboardUrl = typeof route === 'function' ? route(dashboardRoute) : (isAdmin ? '/admin/dashboard' : '/dashboard');

        return (
            <Link href={dashboardUrl}>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                    <LayoutGrid className="h-4 w-4" />
                    Dashboard
                </Button>
            </Link>
        );
    };

    const renderContent = () => {
        const logoPosition = config.navbar_logo_position;
        
        return (
            <div className="mx-auto flex h-16 items-center justify-between px-4 md:max-w-7xl">
                {/* Left section */}
                <div className="flex items-center space-x-4">
                    {logoPosition === 'left' && renderLogo()}
                    {renderMobileMenu()}
                </div>

                {/* Center section */}
                <div className="flex items-center space-x-6">
                    {logoPosition === 'center' && renderLogo()}
                    {renderDesktopMenu()}
                </div>

                {/* Right section */}
                <div className="flex items-center space-x-4">
                    {logoPosition === 'right' && renderLogo()}
                    {renderSearchButton()}
                    {renderAuthButtons()}
                    {renderDashboardButton()}
                </div>
            </div>
        );
    };

    return (
        <nav
            className={`${stickyClass} ${styleClass} ${className}`}
            style={navbarStyle}
            role="navigation"
        >
            {renderContent()}
        </nav>
    );
}
