import { Head, Link, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import {
    Package,
    Eye,
    Heart,
    ChevronLeft,
    ChevronRight,
    Grid,
    List,
    SlidersHorizontal,
    ArrowLeft,
    Building2,
    Search
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { AutoWatermark } from '@/components/Watermark';
import { useState, useEffect } from 'react';
import type { Part, Brand, Category, SearchFilters, AppliedFilters, PaginatedResponse } from '@/types';

interface Props {
    brand: Brand;
    filters: SearchFilters;
    results?: PaginatedResponse<Part>;
    applied_filters?: AppliedFilters;
    search_type?: string;
    query?: string;
    remaining_searches?: number;
}

export default function BrandSearch({ 
    brand, 
    filters, 
    results, 
    applied_filters = {}, 
    search_type = 'all', 
    query = '', 
    remaining_searches 
}: Props) {
    const [searchQuery, setSearchQuery] = useState(query);
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [showFilters, setShowFilters] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [searchType, setSearchType] = useState(search_type);
    const [isSearching, setIsSearching] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [suggestions, setSuggestions] = useState<any[]>([]);

    // Sync search query with props when they change
    useEffect(() => {
        setSearchQuery(query);
    }, [query]);

    // Custom search handler for brand-specific search
    const handleBrandSearch = (searchQuery: string, searchType: string, selectedFilters: any) => {
        if (!searchQuery.trim()) {
            return;
        }

        setIsLoading(true);

        const params = new URLSearchParams({
            q: searchQuery,
            type: searchType,
            ...Object.fromEntries(
                Object.entries({...applied_filters, ...selectedFilters}).filter(([, value]) =>
                    value !== 'all' &&
                    value !== '' &&
                    value !== false &&
                    value !== null &&
                    value !== 'null' &&
                    value !== undefined
                )
            )
        });

        const searchUrl = route('search.brand', brand.slug || brand.id) + '?' + params.toString();

        router.visit(searchUrl, {
            onStart: () => setIsLoading(true),
            onFinish: () => setIsLoading(false),
            onError: (errors) => {
                console.error('Brand search navigation error:', errors);
                setIsLoading(false);
            },
            onCancel: () => setIsLoading(false)
        });
    };

    // Create a custom UnifiedSearchInterface that handles brand-specific search
    const BrandSearchInterface = () => {
        return (
            <UnifiedSearchInterface
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                isAuthenticated={true}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                showFilters={true}
                showSuggestions={true}
                size="lg"
                placeholder={`Search for ${brand.name} parts...`}
                filters={{
                    categories: filters.categories,
                    manufacturers: filters.manufacturers,
                    release_years: filters.release_years
                }}
                // Override the default search behavior
                onCustomSearch={handleBrandSearch}
            />
        );
    };

    const handlePageChange = (page: number) => {
        const currentParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Only preserve valid parameters
        for (const [key, value] of currentParams.entries()) {
            if (value !== 'all' &&
                value !== '' &&
                value !== 'false' &&
                value !== 'null' &&
                value !== null &&
                value !== undefined) {
                params.set(key, value);
            }
        }

        params.set('page', page.toString());
        router.get(window.location.pathname + '?' + params.toString());
    };

    const handleFilterChange = (key: string, value: string) => {
        const currentParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Only preserve valid parameters
        for (const [paramKey, paramValue] of currentParams.entries()) {
            if (paramValue !== 'all' &&
                paramValue !== '' &&
                paramValue !== 'false' &&
                paramValue !== 'null' &&
                paramValue !== null &&
                paramValue !== undefined) {
                params.set(paramKey, paramValue);
            }
        }

        if (value && value !== 'all') {
            params.set(key, value);
        } else {
            params.delete(key);
        }
        params.delete('page'); // Reset to first page when filtering
        router.get(window.location.pathname + '?' + params.toString());
    };

    const PartCard = ({ part }: { part: Part }) => (
        <div className="relative">
            <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-1">
                            {part.name}
                        </h3>
                        {part.part_number && (
                            <p className="text-sm text-gray-600 mb-1">
                                Part #: {part.part_number}
                            </p>
                        )}
                        {part.manufacturer && (
                            <p className="text-sm text-gray-600 mb-2">
                                by {part.manufacturer}
                            </p>
                        )}
                    </div>
                    <Button variant="ghost" size="sm">
                        <Heart className="w-4 h-4" />
                    </Button>
                </div>

                <div className="mb-3">
                    <Badge variant="outline" className="mb-2">
                        {part.category.name}
                    </Badge>
                    {part.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                            {part.description}
                        </p>
                    )}
                </div>

                {part.models && part.models.length > 0 && (
                    <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Compatible with:</p>
                        <div className="flex flex-wrap gap-1">
                            {part.models.slice(0, 3).map((model) => (
                                <Badge key={model.id} variant="secondary" className="text-xs">
                                    {model.brand.name} {model.name}
                                </Badge>
                            ))}
                            {part.models.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                    +{part.models.length - 3} more
                                </Badge>
                            )}
                        </div>
                    </div>
                )}

                <div className="flex justify-between items-center">
                    <Link href={route('parts.show', part.slug || part.id)}>
                        <Button size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                        </Button>
                    </Link>
                </div>
            </CardContent>
            </Card>
            <AutoWatermark />
        </div>
    );

    const PartListItem = ({ part }: { part: Part }) => (
        <div className="relative">
            <Card>
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="flex-1">
                        <div className="flex items-start gap-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                <Package className="w-8 h-8 text-gray-400" />
                            </div>
                            <div className="flex-1">
                                <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                    {part.name}
                                </h3>
                                <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                    {part.part_number && <span>Part #: {part.part_number}</span>}
                                    {part.manufacturer && <span>by {part.manufacturer}</span>}
                                    <Badge variant="outline">{part.category.name}</Badge>
                                </div>
                                {part.description && (
                                    <p className="text-sm text-gray-600 mb-2 line-clamp-1">
                                        {part.description}
                                    </p>
                                )}
                                {part.models && part.models.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                        {part.models.slice(0, 5).map((model) => (
                                            <Badge key={model.id} variant="secondary" className="text-xs">
                                                {model.brand.name} {model.name}
                                            </Badge>
                                        ))}
                                        {part.models.length > 5 && (
                                            <Badge variant="secondary" className="text-xs">
                                                +{part.models.length - 5} more
                                            </Badge>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                            <Heart className="w-4 h-4" />
                        </Button>
                        <Link href={route('parts.show', part.slug || part.id)}>
                            <Button size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                            </Button>
                        </Link>
                    </div>
                </div>
            </CardContent>
            </Card>
            <AutoWatermark />
        </div>
    );

    return (
        <AppLayout key={`brand-search-${brand.id}`}>
            <Head title={`Search ${brand.name} Parts`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-6">
                        <div className="flex items-center gap-2 mb-4">
                            <Link href={route('brands.show', brand.slug || brand.id)}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to {brand.name}
                                </Button>
                            </Link>
                        </div>
                        <div className="flex items-center gap-3 mb-2">
                            <Building2 className="w-8 h-8 text-blue-600" />
                            <h1 className="text-3xl font-bold text-gray-900">
                                Search {brand.name} Parts
                            </h1>
                        </div>
                        <p className="text-gray-600">
                            Find parts specifically for {brand.name} devices
                            {remaining_searches !== undefined && remaining_searches !== -1 && (
                                <span className="ml-2">
                                    • {remaining_searches} searches remaining today
                                </span>
                            )}
                        </p>
                    </div>

                    {/* Search Form */}
                    <Card className="mb-6">
                        <CardContent className="p-6">
                            <BrandSearchInterface />
                        </CardContent>
                    </Card>

                    {/* Results Section */}
                    {isLoading ? (
                        <Card>
                            <CardContent className="text-center py-12">
                                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Searching...
                                </h3>
                                <p className="text-gray-600">
                                    Finding {brand.name} parts for you
                                </p>
                            </CardContent>
                        </Card>
                    ) : results ? (
                        <>
                            {/* Results Header */}
                            <div className="flex items-center justify-between mb-6">
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">
                                        Search Results
                                    </h2>
                                    <p className="text-gray-600">
                                        {results.total} {brand.name} parts found for "{query}"
                                    </p>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('grid')}
                                    >
                                        <Grid className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('list')}
                                    >
                                        <List className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setShowFilters(!showFilters)}
                                    >
                                        <SlidersHorizontal className="w-4 h-4 mr-2" />
                                        Filters
                                    </Button>
                                </div>
                            </div>

                            {/* Filters */}
                            {showFilters && (
                                <Card className="mb-6">
                                    <CardHeader>
                                        <CardTitle>Additional Filters</CardTitle>
                                        <CardDescription>
                                            Narrow down your search within {brand.name} parts
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <Select
                                                value={applied_filters.category_id || 'all'}
                                                onValueChange={(value) => handleFilterChange('category_id', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Category" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Categories</SelectItem>
                                                    {filters.categories.map((category: Category) => (
                                                        <SelectItem key={category.id} value={category.id.toString()}>
                                                            {category.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>

                                            <Select
                                                value={applied_filters.manufacturer || 'all'}
                                                onValueChange={(value) => handleFilterChange('manufacturer', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Manufacturer" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Manufacturers</SelectItem>
                                                    {filters.manufacturers?.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer: string) => (
                                                        <SelectItem key={manufacturer} value={manufacturer}>
                                                            {manufacturer}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>

                                            <Select
                                                value={applied_filters.release_year || 'all'}
                                                onValueChange={(value) => handleFilterChange('release_year', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Year" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Years</SelectItem>
                                                    {filters.release_years?.filter(year => year && year.toString().trim() !== '').map((year: number) => (
                                                        <SelectItem key={year} value={year.toString()}>
                                                            {year}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Results */}
                            {results.data.length > 0 ? (
                                <>
                                    <div className={
                                        viewMode === 'grid'
                                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'
                                            : 'space-y-4 mb-8'
                                    }>
                                        {results.data.map((part) => (
                                            viewMode === 'grid'
                                                ? <PartCard key={part.id} part={part} />
                                                : <PartListItem key={part.id} part={part} />
                                        ))}
                                    </div>

                                    {/* Pagination */}
                                    {results.last_page > 1 && (
                                        <div className="flex items-center justify-between">
                                            <p className="text-sm text-gray-600">
                                                Showing {results.from} to {results.to} of {results.total} results
                                            </p>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handlePageChange(results.current_page - 1)}
                                                    disabled={results.current_page === 1}
                                                >
                                                    <ChevronLeft className="w-4 h-4" />
                                                    Previous
                                                </Button>

                                                {Array.from({ length: Math.min(5, results.last_page) }, (_, i) => {
                                                    const page = i + 1;
                                                    return (
                                                        <Button
                                                            key={page}
                                                            variant={page === results.current_page ? 'default' : 'outline'}
                                                            size="sm"
                                                            onClick={() => handlePageChange(page)}
                                                        >
                                                            {page}
                                                        </Button>
                                                    );
                                                })}

                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handlePageChange(results.current_page + 1)}
                                                    disabled={results.current_page === results.last_page}
                                                >
                                                    Next
                                                    <ChevronRight className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <Card>
                                    <CardContent className="text-center py-12">
                                        <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                            No {brand.name} parts found
                                        </h3>
                                        <p className="text-gray-600 mb-6">
                                            Try adjusting your search terms or filters
                                        </p>
                                        <Button onClick={() => {
                                            // Clear local state
                                            setSearchQuery('');
                                            setSearchType('all');
                                            setIsSearching(false);
                                            setShowSuggestions(false);
                                            setSuggestions([]);

                                            // Navigate to clear server-side state
                                            router.get(route('search.brand', brand.slug || brand.id));
                                        }}>
                                            <Search className="w-4 h-4 mr-2" />
                                            Clear Search
                                        </Button>
                                    </CardContent>
                                </Card>
                            )}
                        </>
                    ) : (
                        /* No search performed yet - show brand info */
                        <Card>
                            <CardContent className="text-center py-12">
                                <Building2 className="w-16 h-16 mx-auto mb-4 text-blue-600" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Search {brand.name} Parts
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    {brand.description || `Find parts specifically for ${brand.name} devices`}
                                </p>
                                <p className="text-sm text-gray-500">
                                    Enter a search term above to find {brand.name} parts
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
