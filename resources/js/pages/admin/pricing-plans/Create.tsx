import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { toast } from 'sonner';
import { useState } from 'react';

interface PricingPlanFormData {
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_public: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    metadata: Record<string, string | number | boolean>;
    // Paddle integration fields
    paddle_price_id_monthly: string;
    paddle_price_id_yearly: string;
    paddle_product_id: string;
    // ShurjoPay integration fields
    shurjopay_price_id_monthly: string;
    shurjopay_price_id_yearly: string;
    shurjopay_product_id: string;
    // Coinbase Commerce integration fields
    coinbase_commerce_price_id_monthly: string;
    coinbase_commerce_price_id_yearly: string;
    coinbase_commerce_product_id: string;
    // Payment method controls
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    crypto_payment_enabled: boolean;
    // Fee configuration
    paddle_fee_percentage: number;
    paddle_fee_fixed: number;
    shurjopay_fee_percentage: number;
    shurjopay_fee_fixed: number;
    coinbase_commerce_fee_percentage: number;
    coinbase_commerce_fee_fixed: number;
    offline_fee_percentage: number;
    offline_fee_fixed: number;
    fee_handling: string;
    show_fees_breakdown: boolean;
    tax_percentage: number;
    tax_inclusive: boolean;
    [key: string]: string | number | boolean | string[] | Record<string, string | number | boolean>;
}

export default function Create() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        display_name: '',
        description: '',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [''],
        search_limit: 20,
        is_active: true as boolean,
        is_public: true as boolean,
        is_default: false as boolean,
        is_popular: false as boolean,
        sort_order: 1,
        metadata: {},
        // Paddle integration fields
        paddle_price_id_monthly: '',
        paddle_price_id_yearly: '',
        paddle_product_id: '',
        // ShurjoPay integration fields
        shurjopay_price_id_monthly: '',
        shurjopay_price_id_yearly: '',
        shurjopay_product_id: '',
        // Coinbase Commerce integration fields
        coinbase_commerce_price_id_monthly: '',
        coinbase_commerce_price_id_yearly: '',
        coinbase_commerce_product_id: '',
        // Payment method controls
        online_payment_enabled: true as boolean,
        offline_payment_enabled: true as boolean,
        crypto_payment_enabled: false as boolean,
        // Fee configuration
        paddle_fee_percentage: 0,
        paddle_fee_fixed: 0,
        shurjopay_fee_percentage: 0,
        shurjopay_fee_fixed: 0,
        coinbase_commerce_fee_percentage: 0,
        coinbase_commerce_fee_fixed: 0,
        offline_fee_percentage: 0,
        offline_fee_fixed: 0,
        fee_handling: 'absorb',
        show_fees_breakdown: false as boolean,
        tax_percentage: 0,
        tax_inclusive: false as boolean,
    });

    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        // Required field validation
        if (!data.name.trim()) {
            newErrors.name = 'Plan name is required';
        }
        if (!data.display_name.trim()) {
            newErrors.display_name = 'Display name is required';
        }
        if (data.price < 0) {
            newErrors.price = 'Price must be 0 or greater';
        }
        if (!data.currency) {
            newErrors.currency = 'Currency is required';
        }
        if (!data.interval) {
            newErrors.interval = 'Billing interval is required';
        }
        if (data.search_limit < -1) {
            newErrors.search_limit = 'Search limit must be -1 or greater';
        }
        if (data.sort_order < 0) {
            newErrors.sort_order = 'Sort order must be 0 or greater';
        }

        // Features validation - at least one non-empty feature required
        const nonEmptyFeatures = data.features.filter(feature => feature.trim() !== '');
        if (nonEmptyFeatures.length === 0) {
            newErrors.features = 'At least one feature is required';
        }

        setValidationErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Clear previous validation errors
        setValidationErrors({});

        // Validate form
        if (!validateForm()) {
            toast.error('Please fix the validation errors before submitting.');
            return;
        }

        // Flash messages from the backend will be handled by FlashMessageHandler
        post(route('admin.pricing-plans.store'));
    };

    const addFeature = () => {
        const newFeatures = [...data.features, ''];
        setData('features', newFeatures);
    };

    const removeFeature = (index: number) => {
        const newFeatures = data.features.filter((_, i) => i !== index);
        setData('features', newFeatures);
    };

    const updateFeature = (index: number, value: string) => {
        const newFeatures = [...data.features];
        newFeatures[index] = value;
        setData('features', newFeatures);
    };

    return (
        <AppLayout>
            <Head title="Create Pricing Plan" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href={route('admin.pricing-plans.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Plans
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Create Pricing Plan</h1>
                        <p className="text-muted-foreground mt-2">
                            Create a new subscription pricing plan
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Tabs defaultValue="basic" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="basic">Basic Info</TabsTrigger>
                            <TabsTrigger value="payment">Payment Methods</TabsTrigger>
                            <TabsTrigger value="integration">Integrations</TabsTrigger>
                            <TabsTrigger value="advanced">Advanced</TabsTrigger>
                        </TabsList>

                        <TabsContent value="basic" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Information</CardTitle>
                                <CardDescription>
                                    Configure the basic details of the pricing plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Plan Name (Internal) *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., premium, enterprise"
                                        className={(errors.name || validationErrors.name) ? 'border-red-500' : ''}
                                        required
                                    />
                                    {(errors.name || validationErrors.name) && <p className="text-sm text-red-500 mt-1">{errors.name || validationErrors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="display_name">Display Name *</Label>
                                    <Input
                                        id="display_name"
                                        value={data.display_name}
                                        onChange={(e) => setData('display_name', e.target.value)}
                                        placeholder="e.g., Premium Plan"
                                        className={(errors.display_name || validationErrors.display_name) ? 'border-red-500' : ''}
                                        required
                                    />
                                    {(errors.display_name || validationErrors.display_name) && <p className="text-sm text-red-500 mt-1">{errors.display_name || validationErrors.display_name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Brief description of the plan"
                                        className={errors.description ? 'border-red-500' : ''}
                                        rows={3}
                                    />
                                    {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Pricing */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Pricing</CardTitle>
                                <CardDescription>
                                    Set the price and billing interval
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="price">Price *</Label>
                                        <Input
                                            id="price"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={data.price}
                                            onChange={(e) => setData('price', parseFloat(e.target.value) || 0)}
                                            className={(errors.price || validationErrors.price) ? 'border-red-500' : ''}
                                            required
                                        />
                                        {(errors.price || validationErrors.price) && <p className="text-sm text-red-500 mt-1">{errors.price || validationErrors.price}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="currency">Currency *</Label>
                                        <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                            <SelectTrigger className={(errors.currency || validationErrors.currency) ? 'border-red-500' : ''}>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="USD">USD</SelectItem>
                                                <SelectItem value="EUR">EUR</SelectItem>
                                                <SelectItem value="GBP">GBP</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {(errors.currency || validationErrors.currency) && <p className="text-sm text-red-500 mt-1">{errors.currency || validationErrors.currency}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="interval">Billing Interval *</Label>
                                    <Select value={data.interval} onValueChange={(value) => setData('interval', value)}>
                                        <SelectTrigger className={(errors.interval || validationErrors.interval) ? 'border-red-500' : ''}>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Monthly</SelectItem>
                                            <SelectItem value="year">Yearly</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {(errors.interval || validationErrors.interval) && <p className="text-sm text-red-500 mt-1">{errors.interval || validationErrors.interval}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="search_limit">Search Limit (per day) *</Label>
                                    <Input
                                        id="search_limit"
                                        type="number"
                                        min="-1"
                                        value={data.search_limit}
                                        onChange={(e) => setData('search_limit', parseInt(e.target.value) || 0)}
                                        placeholder="-1 for unlimited"
                                        className={(errors.search_limit || validationErrors.search_limit) ? 'border-red-500' : ''}
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">Use -1 for unlimited searches</p>
                                    {(errors.search_limit || validationErrors.search_limit) && <p className="text-sm text-red-500 mt-1">{errors.search_limit || validationErrors.search_limit}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="sort_order">Sort Order *</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        min="0"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        className={(errors.sort_order || validationErrors.sort_order) ? 'border-red-500' : ''}
                                        required
                                    />
                                    {(errors.sort_order || validationErrors.sort_order) && <p className="text-sm text-red-500 mt-1">{errors.sort_order || validationErrors.sort_order}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Features */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Features *</CardTitle>
                                <CardDescription>
                                    List the features included in this plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {data.features.map((feature, index) => (
                                    <div key={index} className="flex gap-2">
                                        <Input
                                            value={feature}
                                            onChange={(e) => updateFeature(index, e.target.value)}
                                            placeholder="Enter feature description"
                                            className={(errors.features || validationErrors.features) ? 'border-red-500' : ''}
                                        />
                                        {data.features.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeFeature(index)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addFeature}
                                >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Feature
                                </Button>
                                {(errors.features || validationErrors.features) && <p className="text-sm text-red-500 mt-1">{errors.features || validationErrors.features}</p>}
                            </CardContent>
                        </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="payment" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                                {/* Payment Methods */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Payment Methods</CardTitle>
                                        <CardDescription>
                                            Configure available payment options for this plan
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Online Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow credit card payments via Paddle
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.online_payment_enabled}
                                                onCheckedChange={(checked) => setData('online_payment_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Offline Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow manual payment requests and verification
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.offline_payment_enabled}
                                                onCheckedChange={(checked) => setData('offline_payment_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Crypto Payments</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Allow cryptocurrency payments via Coinbase Commerce
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.crypto_payment_enabled}
                                                onCheckedChange={(checked) => setData('crypto_payment_enabled', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Settings */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Settings</CardTitle>
                                        <CardDescription>
                                            Configure plan visibility and behavior
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Active</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Make this plan available for subscription
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_active}
                                                onCheckedChange={(checked) => setData('is_active', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Public Visibility</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Show this plan to users on pricing pages
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_public}
                                                onCheckedChange={(checked) => setData('is_public', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Default Plan</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Assign to new users by default
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_default}
                                                onCheckedChange={(checked) => setData('is_default', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Popular Plan</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Highlight this plan as popular
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.is_popular}
                                                onCheckedChange={(checked) => setData('is_popular', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="integration" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                                {/* Paddle Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Paddle Integration</CardTitle>
                                        <CardDescription>
                                            Configure Paddle payment gateway settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="paddle_product_id">Paddle Product ID</Label>
                                            <Input
                                                id="paddle_product_id"
                                                value={data.paddle_product_id}
                                                onChange={(e) => setData('paddle_product_id', e.target.value)}
                                                placeholder="pro_01h1abc123def456ghi789jkl"
                                                className={errors.paddle_product_id ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Product ID from your Paddle dashboard
                                            </p>
                                            {errors.paddle_product_id && <p className="text-sm text-red-500 mt-1">{errors.paddle_product_id}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="paddle_price_id_monthly">Monthly Price ID</Label>
                                            <Input
                                                id="paddle_price_id_monthly"
                                                value={data.paddle_price_id_monthly}
                                                onChange={(e) => setData('paddle_price_id_monthly', e.target.value)}
                                                placeholder="pri_01h1abc123def456ghi789jkl"
                                                className={errors.paddle_price_id_monthly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Monthly billing price ID from Paddle
                                            </p>
                                            {errors.paddle_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.paddle_price_id_monthly}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="paddle_price_id_yearly">Yearly Price ID</Label>
                                            <Input
                                                id="paddle_price_id_yearly"
                                                value={data.paddle_price_id_yearly}
                                                onChange={(e) => setData('paddle_price_id_yearly', e.target.value)}
                                                placeholder="pri_01h1def456ghi789jkl123abc"
                                                className={errors.paddle_price_id_yearly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Yearly billing price ID from Paddle
                                            </p>
                                            {errors.paddle_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.paddle_price_id_yearly}</p>}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* ShurjoPay Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>ShurjoPay Integration</CardTitle>
                                        <CardDescription>
                                            Configure ShurjoPay payment gateway settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="shurjopay_product_id">ShurjoPay Product ID</Label>
                                            <Input
                                                id="shurjopay_product_id"
                                                value={data.shurjopay_product_id}
                                                onChange={(e) => setData('shurjopay_product_id', e.target.value)}
                                                placeholder="sp_product_123456"
                                                className={errors.shurjopay_product_id ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Product ID from your ShurjoPay dashboard
                                            </p>
                                            {errors.shurjopay_product_id && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_product_id}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="shurjopay_price_id_monthly">Monthly Price ID</Label>
                                            <Input
                                                id="shurjopay_price_id_monthly"
                                                value={data.shurjopay_price_id_monthly}
                                                onChange={(e) => setData('shurjopay_price_id_monthly', e.target.value)}
                                                placeholder="sp_price_monthly_123456"
                                                className={errors.shurjopay_price_id_monthly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Monthly billing price ID from ShurjoPay
                                            </p>
                                            {errors.shurjopay_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_price_id_monthly}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="shurjopay_price_id_yearly">Yearly Price ID</Label>
                                            <Input
                                                id="shurjopay_price_id_yearly"
                                                value={data.shurjopay_price_id_yearly}
                                                onChange={(e) => setData('shurjopay_price_id_yearly', e.target.value)}
                                                placeholder="sp_price_yearly_123456"
                                                className={errors.shurjopay_price_id_yearly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Yearly billing price ID from ShurjoPay
                                            </p>
                                            {errors.shurjopay_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_price_id_yearly}</p>}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Coinbase Commerce Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Coinbase Commerce Integration</CardTitle>
                                        <CardDescription>
                                            Configure Coinbase Commerce cryptocurrency payment settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="coinbase_commerce_product_id">Coinbase Commerce Product ID</Label>
                                            <Input
                                                id="coinbase_commerce_product_id"
                                                value={data.coinbase_commerce_product_id}
                                                onChange={(e) => setData('coinbase_commerce_product_id', e.target.value)}
                                                placeholder="cb_product_123456"
                                                className={errors.coinbase_commerce_product_id ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Product ID from your Coinbase Commerce dashboard
                                            </p>
                                            {errors.coinbase_commerce_product_id && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_product_id}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="coinbase_commerce_price_id_monthly">Monthly Price ID</Label>
                                            <Input
                                                id="coinbase_commerce_price_id_monthly"
                                                value={data.coinbase_commerce_price_id_monthly}
                                                onChange={(e) => setData('coinbase_commerce_price_id_monthly', e.target.value)}
                                                placeholder="cb_price_monthly_123456"
                                                className={errors.coinbase_commerce_price_id_monthly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Monthly billing price ID from Coinbase Commerce
                                            </p>
                                            {errors.coinbase_commerce_price_id_monthly && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_price_id_monthly}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="coinbase_commerce_price_id_yearly">Yearly Price ID</Label>
                                            <Input
                                                id="coinbase_commerce_price_id_yearly"
                                                value={data.coinbase_commerce_price_id_yearly}
                                                onChange={(e) => setData('coinbase_commerce_price_id_yearly', e.target.value)}
                                                placeholder="cb_price_yearly_123456"
                                                className={errors.coinbase_commerce_price_id_yearly ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Yearly billing price ID from Coinbase Commerce
                                            </p>
                                            {errors.coinbase_commerce_price_id_yearly && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_price_id_yearly}</p>}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="advanced" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                                {/* Fee Configuration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Fee Configuration</CardTitle>
                                        <CardDescription>
                                            Configure payment processing fees and handling
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="fee_handling">Fee Handling</Label>
                                            <Select value={data.fee_handling} onValueChange={(value) => setData('fee_handling', value)}>
                                                <SelectTrigger className={errors.fee_handling ? 'border-red-500' : ''}>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="absorb">Absorb fees (included in price)</SelectItem>
                                                    <SelectItem value="pass_to_customer">Pass to customer (added to price)</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.fee_handling && <p className="text-sm text-red-500 mt-1">{errors.fee_handling}</p>}
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Show Fees Breakdown</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Display fee breakdown to customers
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.show_fees_breakdown}
                                                onCheckedChange={(checked) => setData('show_fees_breakdown', checked)}
                                            />
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_fee_percentage">Paddle Fee %</Label>
                                                <Input
                                                    id="paddle_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.paddle_fee_percentage}
                                                    onChange={(e) => setData('paddle_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.paddle_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.paddle_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.paddle_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="paddle_fee_fixed">Paddle Fee Fixed</Label>
                                                <Input
                                                    id="paddle_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.paddle_fee_fixed}
                                                    onChange={(e) => setData('paddle_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.paddle_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.paddle_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.paddle_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_fee_percentage">ShurjoPay Fee %</Label>
                                                <Input
                                                    id="shurjopay_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.shurjopay_fee_percentage}
                                                    onChange={(e) => setData('shurjopay_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.shurjopay_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.shurjopay_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="shurjopay_fee_fixed">ShurjoPay Fee Fixed</Label>
                                                <Input
                                                    id="shurjopay_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.shurjopay_fee_fixed}
                                                    onChange={(e) => setData('shurjopay_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.shurjopay_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.shurjopay_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.shurjopay_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_fee_percentage">Coinbase Commerce Fee %</Label>
                                                <Input
                                                    id="coinbase_commerce_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.coinbase_commerce_fee_percentage}
                                                    onChange={(e) => setData('coinbase_commerce_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.coinbase_commerce_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.coinbase_commerce_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="coinbase_commerce_fee_fixed">Coinbase Commerce Fee Fixed</Label>
                                                <Input
                                                    id="coinbase_commerce_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.coinbase_commerce_fee_fixed}
                                                    onChange={(e) => setData('coinbase_commerce_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.coinbase_commerce_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.coinbase_commerce_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.coinbase_commerce_fee_fixed}</p>}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="offline_fee_percentage">Offline Payment Fee %</Label>
                                                <Input
                                                    id="offline_fee_percentage"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    value={data.offline_fee_percentage}
                                                    onChange={(e) => setData('offline_fee_percentage', parseFloat(e.target.value) || 0)}
                                                    className={errors.offline_fee_percentage ? 'border-red-500' : ''}
                                                />
                                                {errors.offline_fee_percentage && <p className="text-sm text-red-500 mt-1">{errors.offline_fee_percentage}</p>}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="offline_fee_fixed">Offline Payment Fee Fixed</Label>
                                                <Input
                                                    id="offline_fee_fixed"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.offline_fee_fixed}
                                                    onChange={(e) => setData('offline_fee_fixed', parseFloat(e.target.value) || 0)}
                                                    className={errors.offline_fee_fixed ? 'border-red-500' : ''}
                                                />
                                                {errors.offline_fee_fixed && <p className="text-sm text-red-500 mt-1">{errors.offline_fee_fixed}</p>}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Tax Configuration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Tax Configuration</CardTitle>
                                        <CardDescription>
                                            Configure tax settings for this plan
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="tax_percentage">Tax Percentage</Label>
                                            <Input
                                                id="tax_percentage"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                max="100"
                                                value={data.tax_percentage}
                                                onChange={(e) => setData('tax_percentage', parseFloat(e.target.value) || 0)}
                                                className={errors.tax_percentage ? 'border-red-500' : ''}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Tax percentage to apply to this plan
                                            </p>
                                            {errors.tax_percentage && <p className="text-sm text-red-500 mt-1">{errors.tax_percentage}</p>}
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>Tax Inclusive</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Tax is included in the displayed price
                                                </p>
                                            </div>
                                            <Switch
                                                checked={data.tax_inclusive}
                                                onCheckedChange={(checked) => setData('tax_inclusive', checked)}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>
                    </Tabs>

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Creating...' : 'Create Plan'}
                        </Button>
                        <Link href={route('admin.pricing-plans.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
