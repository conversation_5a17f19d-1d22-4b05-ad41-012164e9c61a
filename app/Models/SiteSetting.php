<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SiteSetting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'value' => 'json',
        'is_active' => 'boolean',
    ];

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "site_setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)
                ->where('is_active', true)
                ->first();
            
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Set a setting value by key.
     */
    public static function set(string $key, $value, string $type = 'string', string $description = '', string $category = 'general'): self
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
                'category' => $category,
                'is_active' => true,
            ]
        );

        // Clear cache
        Cache::forget("site_setting_{$key}");

        return $setting;
    }

    /**
     * Get all settings by category.
     */
    public static function getByCategory(string $category): array
    {
        $cacheKey = "site_settings_category_{$category}";
        
        return Cache::remember($cacheKey, 3600, function () use ($category) {
            return static::where('category', $category)
                ->where('is_active', true)
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache(): void
    {
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("site_setting_{$key}");
        }
        
        $categories = static::distinct('category')->pluck('category');
        foreach ($categories as $category) {
            Cache::forget("site_settings_category_{$category}");
        }
    }

    /**
     * Get default settings configuration.
     */
    public static function getDefaults(): array
    {
        return [
            // Branding Settings
            'site_name' => [
                'value' => env('APP_NAME', 'FixHaat'),
                'type' => 'string',
                'description' => 'The main application/site name displayed throughout the application',
                'category' => 'branding',
            ],
            'site_tagline' => [
                'value' => 'The comprehensive mobile parts database for professionals',
                'type' => 'string',
                'description' => 'Optional tagline or description for the application',
                'category' => 'branding',
            ],
            'site_logo_url' => [
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the site logo image',
                'category' => 'branding',
            ],
            'site_logo_alt' => [
                'value' => 'Site Logo',
                'type' => 'string',
                'description' => 'Alt text for the site logo',
                'category' => 'branding',
            ],
            'site_logo_width' => [
                'value' => 40,
                'type' => 'integer',
                'description' => 'Logo width in pixels',
                'category' => 'branding',
            ],
            'site_logo_height' => [
                'value' => 40,
                'type' => 'integer',
                'description' => 'Logo height in pixels',
                'category' => 'branding',
            ],

            // Parts Management Settings
            'parts_show_verification_status' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show verification status badges in parts interface',
                'category' => 'parts_management',
            ],
            
            // Favicon Settings
            'favicon_ico_url' => [
                'value' => '/favicon.ico',
                'type' => 'string',
                'description' => 'URL to the ICO favicon file',
                'category' => 'favicon',
            ],
            'favicon_svg_url' => [
                'value' => '/favicon.svg',
                'type' => 'string',
                'description' => 'URL to the SVG favicon file',
                'category' => 'favicon',
            ],
            'favicon_png_url' => [
                'value' => '/apple-touch-icon.png',
                'type' => 'string',
                'description' => 'URL to the PNG favicon file (Apple touch icon)',
                'category' => 'favicon',
            ],
            'favicon_sizes' => [
                'value' => ['16x16', '32x32', '48x48'],
                'type' => 'array',
                'description' => 'Available favicon sizes',
                'category' => 'favicon',
            ],

            // Footer Settings
            'footer_enabled' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Enable custom footer display',
                'category' => 'footer',
            ],
            'footer_layout' => [
                'value' => 'simple',
                'type' => 'string',
                'description' => 'Footer layout style (simple, columns, centered)',
                'category' => 'footer',
            ],
            'footer_background_color' => [
                'value' => '#1f2937',
                'type' => 'string',
                'description' => 'Footer background color (hex code)',
                'category' => 'footer',
            ],
            'footer_text_color' => [
                'value' => '#ffffff',
                'type' => 'string',
                'description' => 'Footer text color (hex code)',
                'category' => 'footer',
            ],
            'footer_content' => [
                'value' => 'The comprehensive mobile parts database for professionals',
                'type' => 'string',
                'description' => 'Main footer content/description',
                'category' => 'footer',
            ],
            'footer_copyright' => [
                'value' => '© 2024 FixHaat. All rights reserved.',
                'type' => 'string',
                'description' => 'Copyright text displayed in footer',
                'category' => 'footer',
            ],
            'footer_links' => [
                'value' => [
                    ['title' => 'Privacy Policy', 'url' => '/privacy', 'target' => '_self'],
                    ['title' => 'Terms of Service', 'url' => '/terms', 'target' => '_self'],
                    ['title' => 'Support', 'url' => '/contact', 'target' => '_self']
                ],
                'type' => 'json',
                'description' => 'Footer navigation links',
                'category' => 'footer',
            ],
            'footer_social_links' => [
                'value' => [],
                'type' => 'json',
                'description' => 'Social media links for footer',
                'category' => 'footer',
            ],
            'footer_show_logo' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Display logo in footer',
                'category' => 'footer',
            ],
            'footer_logo_position' => [
                'value' => 'center',
                'type' => 'string',
                'description' => 'Logo position in footer (left, center, right)',
                'category' => 'footer',
            ],
            'footer_menu_ids' => [
                'value' => [],
                'type' => 'json',
                'description' => 'Selected menu IDs for footer navigation columns',
                'category' => 'footer',
            ],
            'footer_newsletter_enabled' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Enable newsletter signup in footer',
                'category' => 'footer',
            ],
            'footer_newsletter_title' => [
                'value' => 'Newsletter',
                'type' => 'string',
                'description' => 'Newsletter section title',
                'category' => 'footer',
            ],
            'footer_newsletter_description' => [
                'value' => 'Subscribe to our newsletter for a weekly dose of news, updates, helpful tips, and exclusive offers.',
                'type' => 'string',
                'description' => 'Newsletter section description',
                'category' => 'footer',
            ],
            'footer_newsletter_placeholder' => [
                'value' => 'Your email',
                'type' => 'string',
                'description' => 'Newsletter email input placeholder text',
                'category' => 'footer',
            ],
            'footer_mobile_apps_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable mobile app download section in footer',
                'category' => 'footer',
            ],
            'footer_mobile_apps_title' => [
                'value' => 'Here is our Mobile Apps',
                'type' => 'string',
                'description' => 'Title for mobile app download section',
                'category' => 'footer',
            ],
            'footer_app_store_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable App Store download button',
                'category' => 'footer',
            ],
            'footer_app_store_url' => [
                'value' => '',
                'type' => 'string',
                'description' => 'URL to App Store listing',
                'category' => 'footer',
            ],
            'footer_play_store_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable Google Play Store download button',
                'category' => 'footer',
            ],
            'footer_play_store_url' => [
                'value' => '',
                'type' => 'string',
                'description' => 'URL to Google Play Store listing',
                'category' => 'footer',
            ],

            // Navbar Settings
            'navbar_enabled' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Enable custom navbar',
                'category' => 'navbar',
            ],
            'navbar_menu_id' => [
                'value' => null,
                'type' => 'integer',
                'description' => 'Selected menu ID for navbar navigation',
                'category' => 'navbar',
            ],
            'navbar_background_color' => [
                'value' => '#ffffff',
                'type' => 'string',
                'description' => 'Navbar background color (hex code)',
                'category' => 'navbar',
            ],
            'navbar_text_color' => [
                'value' => '#1f2937',
                'type' => 'string',
                'description' => 'Navbar text color (hex code)',
                'category' => 'navbar',
            ],
            'navbar_logo_position' => [
                'value' => 'left',
                'type' => 'string',
                'description' => 'Logo position in navbar (left, center, right)',
                'category' => 'navbar',
            ],
            'navbar_show_search' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show search functionality in navbar',
                'category' => 'navbar',
            ],
            'navbar_sticky' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Make navbar sticky on scroll',
                'category' => 'navbar',
            ],
            'navbar_style' => [
                'value' => 'default',
                'type' => 'string',
                'description' => 'Navbar style theme (default, minimal, bold)',
                'category' => 'navbar',
            ],
        ];
    }

    /**
     * Seed default settings.
     */
    public static function seedDefaults(): void
    {
        $defaults = static::getDefaults();
        
        foreach ($defaults as $key => $config) {
            static::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are updated
        static::saved(function () {
            static::clearCache();
        });

        static::deleted(function () {
            static::clearCache();
        });
    }
}
