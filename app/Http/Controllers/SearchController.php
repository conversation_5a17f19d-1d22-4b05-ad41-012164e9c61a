<?php

namespace App\Http\Controllers;

use App\Events\PartViewed;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SearchConfiguration;
use App\Models\User;
use App\Services\SearchService;
use App\Services\CopyProtectionService;
use App\Services\CompatibilityColumnService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SearchController extends Controller
{
    public function __construct(
        private SearchService $searchService,
        private CopyProtectionService $copyProtectionService,
        private CompatibilityColumnService $compatibilityColumnService
    ) {
        //
    }

    /**
     * Show the search page.
     */
    public function index()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page for search
        if (!$user) {
            return redirect()->route('home');
        }

        $filters = $this->searchService->getAvailableFilters();

        return Inertia::render('search/index', [
            'filters' => $filters,
        ]);
    }

    /**
     * Perform search.
     */
    public function search(Request $request)
    {
        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        $user = $request->user();
        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            if ($request->header('X-Inertia')) {
                // For Inertia requests, redirect to subscription plans with error message
                return redirect()->route('subscription.plans')->with([
                    'error' => $results['error'],
                    'message' => $results['message'] ?? 'You have reached your daily search limit.',
                ]);
            }
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/results', $results);
    }

    /**
     * Get search suggestions.
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $user = $request->user();
        $categoryId = $request->get('category_id');
        $brandId = $request->get('brand_id');

        // Get suggestions with user context for limit checking
        $suggestions = $this->searchService->getSuggestions($query, 10, $categoryId, $brandId, $user);

        // Handle error responses (e.g., search limit exceeded)
        if (isset($suggestions['error'])) {
            return response()->json($suggestions, 429);
        }

        return response()->json($suggestions);
    }

    /**
     * Show brand details.
     */
    public function showBrand(Brand $brand)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $brand->load(['models' => function ($query) {
            $query->active()->with('parts');
        }]);

        // Log brand view activity
        if ($user) {
            $user->logActivity(
                'brand_viewed',
                "User viewed brand: {$brand->name}",
                [
                    'brand_id' => $brand->id,
                    'brand_name' => $brand->name,
                    'models_count' => $brand->models->count(),
                ]
            );
        }

        return Inertia::render('search/brand-details', [
            'brand' => $brand,
        ]);
    }

    /**
     * Show category details.
     */
    public function showCategory(Category $category)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $category->load(['parts' => function ($query) {
            $query->active()->with(['models.brand']);
        }, 'children']);

        // Log category view activity
        if ($user) {
            $user->logActivity(
                'category_viewed',
                "User viewed category: {$category->name}",
                [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'parts_count' => $category->parts->count(),
                    'children_count' => $category->children->count(),
                ]
            );
        }

        return Inertia::render('search/category-details', [
            'category' => $category,
        ]);
    }

    /**
     * Show mobile model details.
     */
    public function showModel(MobileModel $model)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $model->load(['brand', 'parts' => function ($query) {
            $query->active()->with('category');
        }]);

        // Log model view activity
        if ($user) {
            $user->logActivity(
                'model_viewed',
                "User viewed model: {$model->name}",
                [
                    'model_id' => $model->id,
                    'model_name' => $model->name,
                    'brand_name' => $model->brand->name ?? null,
                    'release_year' => $model->release_year,
                    'parts_count' => $model->parts->count(),
                ]
            );
        }

        return Inertia::render('search/model-details', [
            'model' => $model,
        ]);
    }

    /**
     * Show part details.
     */
    public function showPart(Part $part)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $part->load(['category', 'models.brand']);
        $relatedParts = $this->searchService->getRelatedParts($part);

        // Get compatibility column configuration for public view
        $compatibilityColumns = $this->compatibilityColumnService->getVisibleColumns(false);

        // Get copy protection configuration for the current user (JavaScript format)
        $copyProtectionConfig = $this->copyProtectionService->getJavaScriptConfig($user);

        // Get guest search configuration for blur effect
        $guestSearchConfig = null;
        if (!$user) {
            // Apply blur effect to compatible models for guest users
            $enablePartialResults = SearchConfiguration::get('enable_partial_results', true);
            $maxVisibleResults = SearchConfiguration::get('guest_max_visible_results', 5);
            $blurIntensity = SearchConfiguration::get('blur_intensity', 'medium');
            $showSignupCta = SearchConfiguration::get('show_signup_cta', true);

            $guestSearchConfig = [
                'enable_partial_results' => $enablePartialResults,
                'max_visible_results' => $maxVisibleResults,
                'blur_intensity' => $blurIntensity,
                'show_signup_cta' => $showSignupCta,
            ];

            // Apply blur logic to compatible models if enabled
            if ($enablePartialResults && $part->models) {
                $models = $part->models->map(function ($model, $index) use ($maxVisibleResults) {
                    $modelArray = $model->toArray();
                    $modelArray['is_blurred'] = $index >= $maxVisibleResults;
                    return $modelArray;
                });

                // Update the part's models with blur information
                $part->setRelation('models', $models);
            }
        }

        // Log part view activity
        if ($user) {
            $user->logActivity(
                'part_viewed',
                "User viewed part: {$part->name}",
                [
                    'part_id' => $part->id,
                    'part_name' => $part->name,
                    'part_number' => $part->part_number,
                    'category' => $part->category->name ?? null,
                    'manufacturer' => $part->manufacturer,
                ]
            );

            // Dispatch part viewed event for plugins
            PartViewed::dispatch($part, $user);
        }

        $data = [
            'part' => $part,
            'relatedParts' => $relatedParts,
            'copyProtectionConfig' => $copyProtectionConfig,
            'compatibilityColumns' => $compatibilityColumns,
        ];

        // Only add guest search config for non-authenticated users
        if (!$user && $guestSearchConfig) {
            $data['guestSearchConfig'] = $guestSearchConfig;
        }

        return Inertia::render('search/part-details', $data);
    }

    /**
     * Get filters data for AJAX requests.
     */
    public function filters()
    {
        return response()->json($this->searchService->getAvailableFilters());
    }

    /**
     * Show category-specific search page.
     */
    public function searchByCategory(Category $category, Request $request)
    {
        /** @var User|null $user */
        $user = $request->user();

        // If no search query provided, show the search interface
        if (!$request->has('q') || empty($request->get('q'))) {
            $filters = $this->searchService->getAvailableFilters();

            return Inertia::render('search/category-search', [
                'category' => $category,
                'filters' => $filters,
            ]);
        }

        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        // Perform the search with category filter
        $cleanedRequest->merge(['category_id' => $category->id]);

        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/category-search', array_merge($results, [
            'category' => $category,
        ]));
    }

    /**
     * Show brand-specific search page.
     */
    public function searchByBrand(Brand $brand, Request $request)
    {
        /** @var User|null $user */
        $user = $request->user();

        // If no search query provided, show the search interface
        if (!$request->has('q') || empty($request->get('q'))) {
            $filters = $this->searchService->getAvailableFilters();

            return Inertia::render('search/brand-search', [
                'brand' => $brand,
                'filters' => $filters,
            ]);
        }

        // Clean up invalid parameters before processing
        $cleanedParams = [];
        foreach ($request->all() as $key => $value) {
            // Only keep valid parameters with valid values
            if (!in_array($value, ['null', null, '', 'all', false], true) && $value !== '') {
                $cleanedParams[$key] = $value;
            }
        }

        // Create a new request with cleaned parameters
        $cleanedRequest = $request->duplicate($cleanedParams);

        // Perform the search with brand filter
        $cleanedRequest->merge(['brand_id' => $brand->id]);
        $results = $this->searchService->searchParts($cleanedRequest, $user);

        if (isset($results['error'])) {
            if ($request->header('X-Inertia')) {
                // For Inertia requests, redirect to subscription plans with error message
                return redirect()->route('subscription.plans')->with([
                    'error' => $results['error'],
                    'message' => $results['message'] ?? 'You have reached your daily search limit.',
                ]);
            }
            return response()->json($results, 429);
        }

        if ($request->wantsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/brand-search', array_merge($results, [
            'brand' => $brand,
        ]));
    }

    /**
     * Show categories listing page for selection.
     */
    public function listCategories()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page
        if (!$user) {
            return redirect()->route('home');
        }

        $categories = Category::active()
            ->withCount('parts')
            ->orderBy('name')
            ->get();

        return Inertia::render('search/categories-list', [
            'categories' => $categories,
        ]);
    }

    /**
     * Show brands listing page for selection.
     */
    public function listBrands()
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to home page
        if (!$user) {
            return redirect()->route('home');
        }

        $brands = Brand::active()
            ->withCount('models')
            ->orderBy('name')
            ->get();

        return Inertia::render('search/brands-list', [
            'brands' => $brands,
        ]);
    }
}
