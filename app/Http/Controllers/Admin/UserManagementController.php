<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\UserApproved;
use App\Mail\UserRejected;
use App\Mail\UserSuspended;
use App\Models\User;
use App\Models\UserActivityLog;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class UserManagementController extends Controller
{
    public function __construct(
        private EmailService $emailService
    ) {
        //
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): Response
    {
        $query = User::query()
            ->with(['approvedBy', 'suspendedBy']);

        // Add counts only if tables exist (for migration compatibility)
        $availableCounts = [];

        if (Schema::hasTable('user_searches')) {
            $availableCounts[] = 'searches';
        }

        if (Schema::hasTable('payment_requests')) {
            $availableCounts[] = 'paymentRequests';
        }

        if (Schema::hasTable('user_activity_logs')) {
            $availableCounts[] = 'activityLogs';
        }

        if (!empty($availableCounts)) {
            $query->withCount($availableCounts);
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->get('approval_status'));
        }

        if ($request->filled('subscription_plan')) {
            $query->where('subscription_plan', $request->get('subscription_plan'));
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'pending_approval' => User::where('approval_status', 'pending')->count(),
            'suspended_users' => User::where('status', 'suspended')->count(),
            'premium_users' => User::where('subscription_plan', 'premium')->count(),
            'email_verified' => User::whereNotNull('email_verified_at')->count(),
            'email_unverified' => User::whereNull('email_verified_at')->count(),
            'fully_active' => User::where('status', 'active')
                ->where('approval_status', 'approved')
                ->whereNotNull('email_verified_at')
                ->count(),
        ];

        // Ensure the users data has the correct structure
        $usersData = [
            'data' => $users->items(),
            'meta' => [
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'from' => $users->firstItem(),
                'to' => $users->lastItem(),
            ],
            'links' => $users->linkCollection()->toArray(),
        ];

        return Inertia::render('admin/Users/<USER>', [
            'users' => $usersData,
            'stats' => $stats,
            'filters' => $request->only([
                'search', 'status', 'approval_status', 'subscription_plan',
                'date_from', 'date_to', 'sort_by', 'sort_order'
            ]),
        ]);
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): Response
    {
        return Inertia::render('admin/Users/<USER>');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'nullable|string|min:8|confirmed',
            'generate_password' => 'boolean',
            'subscription_plan' => 'required|in:free,premium',
            'status' => 'required|in:active,pending,suspended,banned',
            'approval_status' => 'required|in:pending,approved,rejected',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'send_welcome_email' => 'boolean',
        ]);

        // Handle password generation or validation
        if ($validated['generate_password'] ?? false) {
            $password = $this->generateRandomPassword();
            $validated['password'] = Hash::make($password);
        } elseif (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            // If no password provided and not generating, create a random one
            $password = $this->generateRandomPassword();
            $validated['password'] = Hash::make($password);
        }

        // Remove helper fields that shouldn't be stored
        unset($validated['password_confirmation'], $validated['generate_password'], $validated['send_welcome_email']);

        // Create the user
        $user = User::create($validated);

        // Log the activity
        $user->logActivity(
            'user_created',
            'User account created by admin',
            [
                'created_by_admin' => true,
                'initial_status' => $validated['status'],
                'initial_approval_status' => $validated['approval_status'],
            ],
            $request->user()
        );

        // Send welcome email if requested
        if ($request->boolean('send_welcome_email')) {
            try {
                // You'll need to create this mail class
                $this->emailService->send(new \App\Mail\UserWelcome($user, $password ?? null), $user->email, [
                    'user_id' => $user->id,
                    'to_name' => $user->name,
                ]);
            } catch (\Exception $e) {
                // Log email error but don't fail the user creation
                Log::error('Failed to send welcome email', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return redirect()->route('admin.users.show', $user)
            ->with('success', "User '{$user->name}' created successfully." .
                (isset($password) ? " Generated password: {$password}" : ''));
    }

    /**
     * Generate a random password for new users.
     */
    private function generateRandomPassword(): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        for ($i = 0; $i < 12; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * Display the specified user.
     */
    public function show(User $user): Response
    {
        $user->load([
            'subscriptions' => function ($query) {
                $query->latest()->take(5);
            },
            'paymentRequests' => function ($query) {
                $query->latest()->take(10)->with('approvedBy');
            },
            'activityLogs' => function ($query) {
                $query->latest()->take(20)->with('performedBy');
            },
            'approvedBy',
            'suspendedBy',
            'searches' => function ($query) {
                $query->latest()->take(10);
            },
            'favorites' => function ($query) {
                $query->latest()->take(10)->with('favoritable');
            }
        ]);

        $user->loadCount([
            'searches',
            'paymentRequests',
            'activityLogs',
            'favorites'
        ]);

        // Get active subscription
        $activeSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->where('current_period_end', '>', now())
            ->first();

        return Inertia::render('admin/Users/<USER>', [
            'user' => array_merge($user->toArray(), [
                'active_subscription' => $activeSubscription,
            ]),
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'subscription_plan' => 'required|in:free,premium',
            'status' => 'required|in:active,pending,suspended,banned',
            'approval_status' => 'required|in:pending,approved,rejected',
        ]);

        $user->update($validated);

        // Log the activity
        $user->logActivity(
            'profile_updated',
            'User profile updated by admin',
            ['updated_fields' => array_keys($validated)],
            $request->user()
        );

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully.');
    }

    /**
     * Approve a user.
     */
    public function approve(Request $request, User $user): RedirectResponse
    {
        if ($user->approval_status === 'approved') {
            return redirect()->back()
                ->with('error', 'User is already approved.');
        }

        $admin = $request->user();
        $user->approve($admin);

        // Refresh the user model to get updated approval data
        $user->refresh();

        // Send email notification
        try {
            $this->emailService->send(new UserApproved($user, $admin), $user->email, [
                'user_id' => $user->id,
                'to_name' => $user->name,
            ]);
        } catch (\Exception $e) {
            // Log email error but don't fail the approval
            Log::error('Failed to send user approval email', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'admin_id' => $admin->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Log the activity
        $user->logActivity(
            'user_approved',
            'User approved by admin',
            ['email_sent' => true],
            $admin
        );

        return redirect()->back()
            ->with('success', 'User approved successfully and notification email sent.');
    }

    /**
     * Reject a user.
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:1000',
        ]);

        if ($user->approval_status === 'rejected') {
            return redirect()->back()
                ->with('error', 'User is already rejected.');
        }

        $admin = $request->user();
        $user->update([
            'approval_status' => 'rejected',
            'approved_by' => $admin->id,
            'approved_at' => now(),
        ]);

        // Refresh the user model to get updated rejection data
        $user->refresh();

        // Send email notification
        try {
            $this->emailService->send(new UserRejected($user, $admin, $validated['reason'] ?? null), $user->email, [
                'user_id' => $user->id,
                'to_name' => $user->name,
            ]);
        } catch (\Exception $e) {
            // Log email error but don't fail the rejection
            Log::error('Failed to send user rejection email', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'admin_id' => $admin->id,
                'reason' => $validated['reason'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Log the activity
        $user->logActivity(
            'user_rejected',
            'User rejected by admin',
            [
                'reason' => $validated['reason'] ?? null,
                'email_sent' => true,
            ],
            $admin
        );

        return redirect()->back()
            ->with('success', 'User rejected successfully and notification email sent.');
    }

    /**
     * Suspend a user.
     */
    public function suspend(Request $request, User $user): RedirectResponse
    {
        // Prevent admin users from being suspended
        if ($user->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Admin users cannot be suspended.');
        }

        $validated = $request->validate([
            'reason' => 'required|string|max:1000',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $expiresAt = isset($validated['expires_at']) && $validated['expires_at'] ? \Carbon\Carbon::parse($validated['expires_at']) : null;
        $admin = $request->user();

        $user->suspend($admin, $validated['reason'], $expiresAt);

        // Refresh the user model to get updated suspension data
        $user->refresh();

        // Send email notification
        try {
            $this->emailService->send(new UserSuspended($user, $admin, $validated['reason'], $expiresAt), $user->email, [
                'user_id' => $user->id,
                'to_name' => $user->name,
            ]);
        } catch (\Exception $e) {
            // Log email error but don't fail the suspension
            Log::error('Failed to send user suspension email', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'admin_id' => $admin->id,
                'reason' => $validated['reason'],
                'expires_at' => $expiresAt?->toDateTimeString(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Log the activity
        $user->logActivity(
            'user_suspended',
            'User suspended by admin',
            [
                'reason' => $validated['reason'],
                'expires_at' => $expiresAt?->toDateTimeString(),
                'email_sent' => true,
            ],
            $admin
        );

        return redirect()->back()
            ->with('success', 'User suspended successfully and notification email sent.');
    }

    /**
     * Unsuspend a user.
     */
    public function unsuspend(Request $request, User $user): RedirectResponse
    {
        if (!$user->isSuspended()) {
            return redirect()->back()
                ->with('error', 'User is not suspended.');
        }

        $user->unsuspend();

        // Log the activity
        $user->logActivity(
            'user_unsuspended',
            'User unsuspended by admin',
            [],
            $request->user()
        );

        return redirect()->back()
            ->with('success', 'User unsuspended successfully.');
    }

    /**
     * Delete a user.
     */
    public function destroy(Request $request, User $user): RedirectResponse
    {
        if ($user->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Cannot delete admin users.');
        }

        // Log the activity before deletion
        UserActivityLog::create([
            'user_id' => $user->id,
            'activity_type' => 'user_deleted',
            'description' => 'User account deleted by admin',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'metadata' => [
                'deleted_user_email' => $user->email,
                'deleted_user_name' => $user->name,
            ],
            'performed_by' => $request->user()->id,
        ]);

        $userName = $user->name;
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', "User '{$userName}' deleted successfully.");
    }

    /**
     * Bulk approve users.
     */
    public function bulkApprove(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $users = User::whereIn('id', $validated['user_ids'])
            ->where('approval_status', 'pending')
            ->get();

        $admin = $request->user();
        $approvedCount = 0;
        $emailErrors = 0;

        foreach ($users as $user) {
            $user->approve($admin);

            // Send email notification
            try {
                $this->emailService->send(new UserApproved($user, $admin), $user->email, [
                    'user_id' => $user->id,
                    'to_name' => $user->name,
                ]);
            } catch (\Exception $e) {
                $emailErrors++;
                Log::error('Failed to send user approval email (bulk)', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
            }

            $user->logActivity(
                'user_approved',
                'User approved by admin (bulk action)',
                ['email_sent' => true],
                $admin
            );
            $approvedCount++;
        }

        $message = "Successfully approved {$approvedCount} users.";
        if ($emailErrors > 0) {
            $message .= " However, {$emailErrors} email notifications failed to send.";
        }

        return redirect()->back()
            ->with('success', $message);
    }

    /**
     * Change a user's password.
     */
    public function changePassword(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'password' => 'required|string|min:8|confirmed',
            'generate_password' => 'boolean',
            'send_notification' => 'boolean',
        ]);

        $admin = $request->user();
        $generatedPassword = null;

        // Handle password generation or use provided password
        if ($validated['generate_password'] ?? false) {
            $generatedPassword = $this->generateRandomPassword();
            $password = $generatedPassword;
        } else {
            $password = $validated['password'];
        }

        // Update the user's password
        $user->update([
            'password' => Hash::make($password),
            'password_changed_at' => now(),
        ]);

        // Log the activity
        $user->logActivity(
            'password_changed',
            'Password changed by admin',
            [
                'changed_by_admin' => true,
                'password_generated' => !empty($generatedPassword),
            ],
            $admin
        );

        // Send notification email if requested
        if ($request->boolean('send_notification')) {
            try {
                // You'll need to create this mail class
                $this->emailService->send(new \App\Mail\PasswordChanged($user, $generatedPassword), $user->email, [
                    'user_id' => $user->id,
                    'to_name' => $user->name,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send password change notification', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $message = "Password updated successfully for {$user->name}.";
        if ($generatedPassword) {
            $message .= " Generated password: {$generatedPassword}";
        }

        return redirect()->back()
            ->with('success', $message);
    }

    /**
     * Manually verify a user's email address.
     */
    public function verifyEmail(Request $request, User $user): RedirectResponse
    {
        $admin = $request->user();

        if ($user->hasVerifiedEmail()) {
            return redirect()->back()
                ->with('info', 'User email is already verified.');
        }

        // Mark email as verified
        $user->update([
            'email_verified_at' => now(),
        ]);

        // If user status is pending AND approval is approved, change to active after email verification
        if ($user->status === 'pending' && $user->approval_status === 'approved') {
            $user->update(['status' => 'active']);
        }

        // Log the activity
        $user->logActivity(
            'email_verified_manually',
            'Email verified manually by admin',
            ['verified_by_admin' => true],
            $admin
        );

        return redirect()->back()
            ->with('success', "Email verified successfully for {$user->name}.");
    }

    /**
     * Unverify a user's email address.
     */
    public function unverifyEmail(Request $request, User $user): RedirectResponse
    {
        $admin = $request->user();

        if (!$user->hasVerifiedEmail()) {
            return redirect()->back()
                ->with('info', 'User email is already unverified.');
        }

        // Mark email as unverified
        $user->update([
            'email_verified_at' => null,
        ]);

        // If user status is active, change to pending after email unverification
        if ($user->status === 'active' && $user->approval_status === 'approved') {
            $user->update(['status' => 'pending']);
        }

        // Log the activity
        $user->logActivity(
            'email_unverified_manually',
            'Email unverified manually by admin',
            ['unverified_by_admin' => true],
            $admin
        );

        return redirect()->back()
            ->with('success', "Email unverified for {$user->name}.");
    }
}
